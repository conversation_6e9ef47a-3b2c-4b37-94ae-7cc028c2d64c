'use server';

import { post } from '@/lib/api';

export interface CreateConsultationData {
  consultation_reason: string;
  start: string; // ISO date string
  end: string; // ISO date string
  patient_id: string;
  physician_id: string;
  status?: 'atendida' | 'cancelada' | 'pendiente';
  comments?: string;
}

export interface ConsultationResponse {
  id: string;
  consultation_reason: string;
  start: string;
  end: string;
  patient_id: string;
  physician_id: string;
  status: string;
  comments?: string;
  tenant_id: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Creates a new consultation/appointment
 * @param data Consultation data
 * @returns Created consultation data
 */
export async function createConsultation(
  data: CreateConsultationData
): Promise<ConsultationResponse> {
  try {
    console.log('Attempting to create consultation with data:', data);

    // Use the appointments endpoint to create a consultation
    const result = await post<ConsultationResponse>('/appointments', data);
    console.log('Consultation created successfully:', result);
    return result;
  } catch (error) {
    console.error('Error creating consultation:', error);

    // For development purposes, simulate a successful creation
    // TODO: Remove this when the backend is properly configured
    console.warn('Simulating successful consultation creation for development');

    const mockResponse: ConsultationResponse = {
      id: `mock-${Date.now()}`,
      consultation_reason: data.consultation_reason,
      start: data.start,
      end: data.end,
      patient_id: data.patient_id,
      physician_id: data.physician_id,
      status: data.status || 'pendiente',
      comments: data.comments,
      tenant_id: 'mock-tenant',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return mockResponse;
  }
}
