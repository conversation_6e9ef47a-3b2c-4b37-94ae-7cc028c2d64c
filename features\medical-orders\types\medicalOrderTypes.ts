// Medical Order Types based on API documentation

export type MedicalOrderType = 
  | 'study-authorization'
  | 'certification'
  | 'hospitalization-request'
  | 'appointment-request'
  | 'medication'
  | 'medication-authorization';

export interface Medication {
  monodrug: string;
  dose: string;
  dose_units: string;
  frecuency: string;
  duration: string;
  duration_units: string;
  observations?: string;
  authorized?: boolean;
}

export interface CreateMedicalOrderRequest {
  patient_id: string;
  cat_study_type_id?: number;
  request_reason?: string;
  description_type?: string;
  application_date?: string;
  file?: string; // Base64 encoded file
  category_cie_diez_id?: number;
  hospitalization_reason?: string;
  cat_speciality_id?: number;
  medications?: Medication[];
}

export interface MedicalOrder {
  id: string;
  url: string;
  request_date: string;
  organization_name: string;
  physician_name: string;
  patient_name?: string;
  order_type: string;
  tenant_id?: string;
}

export interface MedicalOrderResponse {
  data: MedicalOrder[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface CreateMedicalOrderResponse {
  message: string;
  order_id: string;
}

// Common dose units
export const DOSE_UNITS = [
  'mg',
  'ml',
  'gotas',
  'unidades'
] as const;

// Common duration units
export const DURATION_UNITS = [
  'días',
  'semanas',
  'meses'
] as const;

// Common frequencies
export const FREQUENCIES = [
  'Cada 8 horas',
  'Cada 12 horas',
  'Una vez al día',
  'Cada 24 horas',
  'Dos veces al día'
] as const;

export type DoseUnit = typeof DOSE_UNITS[number];
export type DurationUnit = typeof DURATION_UNITS[number];
export type Frequency = typeof FREQUENCIES[number];
