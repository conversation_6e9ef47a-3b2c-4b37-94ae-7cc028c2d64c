'use client';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { getMedicalOrders } from '@/features/medical-orders/actions/medicalOrderActions';
import { MedicalOrder, MedicalOrderType } from '@/features/medical-orders/types/medicalOrderTypes';
import { PatientsPagination } from '@/features/patients/components/PatientsPagination';

const MedicalOrdersPage = () => {
  const router = useRouter();
  const [orders, setOrders] = useState<MedicalOrder[]>([]);
  const [totalOrders, setTotalOrders] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<MedicalOrderType | ''>('');

  const ordersPerPage = 10;

  // Fetch medical orders data with pagination
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        const result = await getMedicalOrders(
          currentPage,
          ordersPerPage,
          filterType || undefined
        );
        setOrders(result.data || []);
        setTotalOrders(result.total || 0);
      } catch (error) {
        console.error('Error fetching medical orders:', error);
        toast.error('Error al cargar las órdenes médicas');
        setOrders([]);
        setTotalOrders(0);
      }
    };

    fetchOrders();
  }, [currentPage, filterType]);

  const totalPages = Math.ceil(totalOrders / ordersPerPage);

  const handleCreateOrder = () => {
    router.push('/dashboard/ordenes-medicas/nueva');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getOrderTypeLabel = (type: string) => {
    const typeLabels: Record<string, string> = {
      'medication': 'Medicación',
      'study-authorization': 'Autorización de Estudio',
      'certification': 'Certificado',
      'hospitalization-request': 'Solicitud de Hospitalización',
      'appointment-request': 'Solicitud de Turno',
      'medication-authorization': 'Autorización de Medicación'
    };
    return typeLabels[type] || type;
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header Section */}
      <div className="flex-shrink-0 p-6 bg-white border-b">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900">Órdenes Médicas</h1>
          <button
            onClick={handleCreateOrder}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            + Nueva Orden
          </button>
        </div>

        {/* Search and Filter Bar */}
        <div className="flex gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Buscar órdenes médicas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="w-64">
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as MedicalOrderType | '')}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Todos los tipos</option>
              <option value="medication">Medicación</option>
              <option value="study-authorization">Autorización de Estudio</option>
              <option value="certification">Certificado</option>
              <option value="hospitalization-request">Solicitud de Hospitalización</option>
              <option value="appointment-request">Solicitud de Turno</option>
              <option value="medication-authorization">Autorización de Medicación</option>
            </select>
          </div>
        </div>
      </div>

      {/* Table Section - Scrollable */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          <div className="w-full rounded-md border border-gray-200 bg-white">
            <table className="w-full border-collapse">
              <thead className="sticky top-0 bg-white">
                <tr className="border-b border-gray-200">
                  <th className="py-4 px-6 text-left font-medium text-gray-500">
                    Paciente
                  </th>
                  <th className="py-4 px-6 text-left font-medium text-gray-500">
                    Tipo de Orden
                  </th>
                  <th className="py-4 px-6 text-left font-medium text-gray-500">
                    Fecha de Solicitud
                  </th>
                  <th className="py-4 px-6 text-left font-medium text-gray-500">
                    Médico
                  </th>
                  <th className="py-4 px-6 text-left font-medium text-gray-500">
                    Organización
                  </th>
                  <th className="py-4 px-6 text-center font-medium text-gray-500">
                    Acciones
                  </th>
                </tr>
              </thead>
              <tbody className="[&_tr:last-child]:border-0">
                {orders.map((order) => (
                  <tr
                    key={order.id}
                    className="border-b border-gray-100 hover:bg-gray-50 transition-colors"
                  >
                    <td className="py-4 px-6 text-gray-900 font-medium">
                      {order.patient_name || 'N/A'}
                    </td>
                    <td className="py-4 px-6 text-gray-600">
                      {getOrderTypeLabel(order.order_type)}
                    </td>
                    <td className="py-4 px-6 text-gray-600">
                      {formatDate(order.request_date)}
                    </td>
                    <td className="py-4 px-6 text-gray-600">
                      {order.physician_name}
                    </td>
                    <td className="py-4 px-6 text-gray-600">
                      {order.organization_name}
                    </td>
                    <td className="py-4 px-6 text-center">
                      <div className="flex justify-center gap-2">
                        <button
                          onClick={() => window.open(order.url, '_blank')}
                          className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                        >
                          Ver
                        </button>
                        <button
                          onClick={() => router.push(`/dashboard/ordenes-medicas/${order.id}/editar`)}
                          className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                        >
                          Editar
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {orders.length === 0 && (
              <div className="py-12 text-center text-gray-500">
                No se encontraron órdenes médicas
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Pagination Section - Fixed at bottom */}
      {totalPages > 1 && (
        <div className="py-2 border-t bg-white">
          <PatientsPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
            totalItems={totalOrders}
            itemsPerPage={ordersPerPage}
            currentPageItemsCount={orders.length}
          />
        </div>
      )}
    </div>
  );
};

export default MedicalOrdersPage;
