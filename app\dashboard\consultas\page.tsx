'use client';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { getConsultations } from '@/features/consultation/actions/fetchConsultation';
import { SearchAndFilterBarConsultations } from '@/features/consultation/components/SearchAndFilterBarConsultations';
import { ConsultationsTable } from '@/features/consultation/components/ConsultationsTable';
import { PatientsPagination } from '@/features/patients/components/PatientsPagination';
import { useConsultationFiltering } from '@/features/consultation/hooks/useConsultationFiltering';

import {
  DashboardConsultation,
  ConsultationSortCriteria,
  ConsultationSortDirection
} from '@/features/consultation/types/consultationTypes';

const Page = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [consultations, setConsultations] = useState<DashboardConsultation[]>([]);
  const [totalConsultations, setTotalConsultations] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState<ConsultationSortCriteria | null>(null);
  const [sortDirection, setSortDirection] = useState<ConsultationSortDirection>('asc');

  const consultationsPerPage = 10;

  // Fetch consultations data with pagination
  useEffect(() => {
    const fetchConsultations = async () => {

      try {
        const result = await getConsultations(currentPage, consultationsPerPage);
        setConsultations(result.consultations || []);
        setTotalConsultations(result.total || 0);
        if (!result.consultations || result.consultations.length === 0) {
          toast.info('No hay consultas que mostrar');
        }
      } catch (error) {
        console.error('Error fetching consultations:', error);
        toast.error('Error al cargar las consultas');
        setConsultations([]);
        setTotalConsultations(0);
      }
    };

    fetchConsultations();
  }, [currentPage, consultationsPerPage]);

  // Use the custom hook for filtering and sorting consultations (client-side for search)
  const { filteredAndSortedConsultations } = useConsultationFiltering({
    consultations,
    searchTerm,
    sortBy,
    sortDirection
  });

  // Calculate pagination based on whether we're searching or not
  const isSearching = searchTerm.trim().length > 0;
  const totalPages = isSearching
    ? Math.ceil(filteredAndSortedConsultations.length / consultationsPerPage)
    : Math.ceil(totalConsultations / consultationsPerPage);

  // For search, use client-side pagination; for normal view, use server data
  const paginatedConsultations = isSearching
    ? filteredAndSortedConsultations.slice(
        (currentPage - 1) * consultationsPerPage,
        currentPage * consultationsPerPage
      )
    : consultations;

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // When search is cleared, refetch data for current page
  useEffect(() => {
    if (!searchTerm.trim()) {
      // Only refetch if we're not already on page 1 or if we have no data
      if (currentPage !== 1 || consultations.length === 0) {
        const fetchConsultations = async () => {
          try {
            const result = await getConsultations(currentPage, consultationsPerPage);
            setConsultations(result.consultations || []);
            setTotalConsultations(result.total || 0);
          } catch (error) {
            console.error('Error fetching consultations:', error);
            toast.error('Error al cargar las consultas');
          }
        };
        fetchConsultations();
      }
    }
  }, [searchTerm, currentPage, consultationsPerPage, consultations.length]);

  // Handler functions
  const handleViewConsultation = (consultationId: string) => {
    router.push(`/dashboard/consultas/${consultationId}/consulta`);
  };

  const handleViewPatientProfile = (patientId: string) => {
    router.push(`/dashboard/pacientes/${patientId}/perfil`);
  };

  const handleSortChange = (criteria: ConsultationSortCriteria) => {
    if (sortBy === criteria) {
      // Toggle direction if clicking the same criteria
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new criteria and reset direction to ascending
      setSortBy(criteria);
      setSortDirection('asc');
    }
  };

  const handleEditConsultation = (consultationId: string) => {
    // TODO: Implement edit consultation functionality
    toast.info('Funcionalidad de edición en desarrollo');
    console.log('Edit consultation:', consultationId);
  };

  const handleDeleteConsultation = (consultation: DashboardConsultation) => {
    // TODO: Implement delete consultation functionality
    toast.info('Funcionalidad de eliminación en desarrollo');
    console.log('Delete consultation:', consultation);
  };

  const handleAddConsultation = () => {
    router.push('/dashboard/consultas/nueva');
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header Section with Search and Filters */}
      <SearchAndFilterBarConsultations
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        sortBy={sortBy}
        onSortChange={handleSortChange}
        onAddConsultation={handleAddConsultation}
      />

      {/* Table Section - Takes remaining space and allows scrolling */}
      <div className="flex-1 min-h-0">
        <ConsultationsTable
          consultations={paginatedConsultations}
          onViewConsultation={handleViewConsultation}
          onViewPatientProfile={handleViewPatientProfile}
          onEditConsultation={handleEditConsultation}
          onDeleteConsultation={handleDeleteConsultation}
        />
      </div>

      {/* Pagination - Always at bottom when there are multiple pages */}
      {totalPages > 1 && (
        <PatientsPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
          totalItems={
            isSearching ? filteredAndSortedConsultations.length : totalConsultations
          }
          itemsPerPage={consultationsPerPage}
          currentPageItemsCount={paginatedConsultations.length}
        />
      )}
    </div>
  );
};

export default Page;
