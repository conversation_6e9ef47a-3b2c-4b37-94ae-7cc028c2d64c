import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(req: NextRequest) {
  // Verificar si el usuario está autenticado usando el token personalizado
  const authToken = req.cookies.get('auth_token');
  const tenantId = req.cookies.get('tenant_id');

  // Rutas que requieren autenticación
  const protectedPaths = ['/dashboard'];
  const isProtectedPath = protectedPaths.some(path =>
    req.nextUrl.pathname.startsWith(path)
  );

  // Si es una ruta protegida y no hay token, redirigir al login
  if (isProtectedPath && !authToken) {
    console.log('🚫 Acceso denegado: No hay token de autenticación');
    return NextResponse.redirect(new URL('/auth/login', req.url));
  }

  // Si hay token pero no tenant_id y está intentando acceder al dashboard,
  // redirigir al onboarding
  if (isProtectedPath && authToken && !tenantId) {
    console.log('🔄 Redirigiendo al onboarding: Falta tenant_id');
    return NextResponse.redirect(new URL('/onboarding/1', req.url));
  }

  // Log para debugging
  if (isProtectedPath) {
    console.log('🔐 Middleware - Ruta protegida:', req.nextUrl.pathname);
    console.log('🎫 Token presente:', !!authToken);
    console.log('🏢 Tenant ID presente:', !!tenantId);
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/dashboard/:path*',
    '/debug-patients/:path*',  // Incluir la ruta de debug
    '/'
  ]
};
