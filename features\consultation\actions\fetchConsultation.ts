import consultations from '@/features/consultation/helpers/consultationList.json';
import { DashboardConsultation } from '../types/consultationTypes';
import { get } from '@/lib/api';

// Debug: Log imported consultations
console.log('🔍 Imported consultations data:', {
  length: consultations.length,
  firstItem: consultations[0],
  isArray: Array.isArray(consultations)
});

// Define the expected response structure from the API
export interface GetConsultationsResponse {
  consultations: DashboardConsultation[];
  total: number;
}

/**
 * Fetches a list of consultations with pagination.
 * @param page The page number to fetch (default: 1).
 * @param pageSize The number of consultations per page (default: 10 for pagination).
 * @returns A promise resolving to the list of consultations and total count.
 */
// Set to true to use mock data, false to use real API
// TODO: Change this to false when backend has consultation data
const USE_MOCK_DATA = true;

export async function getConsultations(
  page: number = 1,
  pageSize: number = 10,
  forceMockData?: boolean
): Promise<GetConsultationsResponse> {
  try {
    const shouldUseMockData = forceMockData !== undefined ? forceMockData : USE_MOCK_DATA;
    console.log('🔍 getConsultations called with:', { page, pageSize, shouldUseMockData });

    if (shouldUseMockData) {
      console.log('📊 Using mock data, total consultations available:', consultations.length);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      // Calculate pagination
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedConsultations = consultations.slice(startIndex, endIndex);

      console.log('📄 Returning paginated consultations:', {
        startIndex,
        endIndex,
        paginatedCount: paginatedConsultations.length,
        total: consultations.length
      });

      return {
        consultations: paginatedConsultations as DashboardConsultation[],
        total: consultations.length
      };
    } else {
      // Try to get appointments from the backend and transform them to consultations
      try {
        const response = await get<any>(`/appointments/user?page=${page}&limit=${pageSize}`);

        // Transform appointments to consultation format
        const transformedConsultations: DashboardConsultation[] = response.data?.map((appointment: any) => ({
          id: appointment.id,
          patientId: appointment.patient_id,
          patientName: appointment.patient?.name || 'Paciente',
          patientLastName: appointment.patient?.last_name || '',
          consultationDate: appointment.start,
          reason: appointment.consultation_reason,
          type: 'presencial', // Default type
          amount: '$0', // Default amount
          doctorName: appointment.physician?.name || 'Doctor'
        })) || [];

        return {
          consultations: transformedConsultations,
          total: response.total || transformedConsultations.length
        };
      } catch (apiError) {
        console.warn('Failed to fetch from API, falling back to mock data:', apiError);

        // Fallback to mock data if API fails
        await new Promise(resolve => setTimeout(resolve, 300));
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedConsultations = consultations.slice(startIndex, endIndex);

        return {
          consultations: paginatedConsultations as DashboardConsultation[],
          total: consultations.length
        };
      }
    }
  } catch (error) {
    console.error('Error fetching consultations:', error);
    throw error;
  }
}

// Legacy function for backward compatibility
const FetchConsultations = () => {
  return consultations as DashboardConsultation[];
};

export default FetchConsultations;
