'use client';

import React from 'react';
import Diagnosis from './diagnosisForm/Diagnosis';
import LaboratoryRequest from './diagnosisForm/LaboratoryRequest';
import Procedures from './diagnosisForm/Procedures';
import Recipes from './diagnosisForm/Recipes';
import TreatementPlan from './diagnosisForm/TreatementPlan';

const DiagnosisForm = () => {
  return (
    <div className="mx-auto max-w-screen-lg justify-center px-4 py-6 sm:px-6 lg:px-8 min-h-[600px]">
      <h1 className="mb-6 text-center text-2xl font-semibold sm:text-3xl">
        Diagnostico y tratamiento
      </h1>
      <div className="flex flex-col justify-center space-y-6">
        <Diagnosis />
        <LaboratoryRequest />
        <Procedures />
        <Recipes />
        <TreatementPlan />
      </div>
    </div>
  );
};

export default DiagnosisForm;
