'use client';
import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { toast } from 'sonner';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { getMedicalOrderById, updateMedicalOrder } from '@/features/medical-orders/actions/medicalOrderActions';
import { MedicalOrder } from '@/features/medical-orders/types/medicalOrderTypes';

const EditMedicalOrderPage = () => {
  const router = useRouter();
  const params = useParams();
  const orderId = params.id as string;
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [order, setOrder] = useState<MedicalOrder | null>(null);
  const [file, setFile] = useState<string>('');

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        const orderData = await getMedicalOrderById(orderId);
        setOrder(orderData);
      } catch (error) {
        console.error('Error fetching order:', error);
        toast.error('Error al cargar la orden médica');
        router.push('/dashboard/ordenes-medicas');
      } finally {
        setIsLoading(false);
      }
    };

    if (orderId) {
      fetchOrder();
    }
  }, [orderId, router]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const base64String = event.target?.result as string;
        // Remove the data:mime;base64, prefix
        const base64Data = base64String.split(',')[1];
        setFile(base64Data);
      };
      reader.readAsDataURL(selectedFile);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!file) {
      toast.error('Por favor seleccione un archivo');
      return;
    }

    setIsSubmitting(true);
    try {
      await updateMedicalOrder(orderId, { file });
      toast.success('Orden médica actualizada exitosamente');
      router.push('/dashboard/ordenes-medicas');
    } catch (error) {
      console.error('Error updating medical order:', error);
      toast.error('Error al actualizar la orden médica');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getOrderTypeLabel = (type: string) => {
    const typeLabels: Record<string, string> = {
      'medication': 'Medicación',
      'study-authorization': 'Autorización de Estudio',
      'certification': 'Certificado',
      'hospitalization-request': 'Solicitud de Hospitalización',
      'appointment-request': 'Solicitud de Turno',
      'medication-authorization': 'Autorización de Medicación'
    };
    return typeLabels[type] || type;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando orden médica...</p>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <p className="text-gray-600 mb-4">No se pudo cargar la orden médica</p>
          <Button onClick={() => router.push('/dashboard/ordenes-medicas')}>
            Volver a Órdenes Médicas
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex-shrink-0 bg-white border-b">
        <div className="p-6">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4 text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Regresar
          </Button>
          
          <h1 className="text-2xl font-bold text-gray-900">Editar Orden Médica</h1>
        </div>
      </div>

      {/* Content - Scrollable */}
      <div className="flex-1 overflow-auto bg-gray-50">
        <div className="p-6">
          <div className="max-w-2xl mx-auto space-y-6">
            {/* Order Information */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Información de la Orden</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tipo de Orden
                  </label>
                  <p className="text-gray-900">{getOrderTypeLabel(order.order_type)}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Fecha de Solicitud
                  </label>
                  <p className="text-gray-900">{formatDate(order.request_date)}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Paciente
                  </label>
                  <p className="text-gray-900">{order.patient_name || 'N/A'}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Médico
                  </label>
                  <p className="text-gray-900">{order.physician_name}</p>
                </div>
                
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Organización
                  </label>
                  <p className="text-gray-900">{order.organization_name}</p>
                </div>
              </div>
            </div>

            {/* Current File */}
            {order.url && (
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Archivo Actual</h2>
                <div className="flex items-center justify-between">
                  <p className="text-gray-600">Archivo disponible</p>
                  <Button
                    variant="outline"
                    onClick={() => window.open(order.url, '_blank')}
                  >
                    Ver Archivo
                  </Button>
                </div>
              </div>
            )}

            {/* Update File Form */}
            <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Actualizar Archivo</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nuevo Archivo *
                  </label>
                  <input
                    type="file"
                    onChange={handleFileChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    required
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Formatos permitidos: PDF, DOC, DOCX, JPG, JPEG, PNG
                  </p>
                </div>

                <div className="flex justify-end space-x-4 pt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.back()}
                    disabled={isSubmitting}
                  >
                    Cancelar
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {isSubmitting ? 'Actualizando...' : 'Actualizar Orden'}
                  </Button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditMedicalOrderPage;
