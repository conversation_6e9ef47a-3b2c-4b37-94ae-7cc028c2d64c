'use server';

import { post } from '../../../lib/api';

// Define the schedule configuration data type
export interface PhysicianScheduleData {
  physician_id: string;
  day_of_week: number; // 0 = Sunday, 1 = Monday, etc.
  start_time: string; // "09:00"
  end_time: string; // "17:00"
  slot_duration: number; // Duration in minutes (e.g., 30)
  is_available: boolean;
}

// Define the response type
export interface PhysicianScheduleResponse {
  id: string;
  physician_id: string;
  day_of_week: number;
  start_time: string;
  end_time: string;
  slot_duration: number;
  is_available: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Configure physician schedule for a specific day
 * @param data Schedule configuration data
 * @returns Created schedule configuration
 */
export async function configurePhysicianSchedule(
  data: PhysicianScheduleData
): Promise<PhysicianScheduleResponse> {
  try {
    console.log('Configuring physician schedule with data:', data);

    // TODO: Replace with actual API call when backend endpoint is available
    // For now, simulate successful configuration
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay

    const mockResponse: PhysicianScheduleResponse = {
      id: `schedule-${Date.now()}`,
      physician_id: data.physician_id,
      day_of_week: data.day_of_week,
      start_time: data.start_time,
      end_time: data.end_time,
      slot_duration: data.slot_duration,
      is_available: data.is_available,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('Physician schedule configured successfully (simulated):', mockResponse);
    return mockResponse;
  } catch (error) {
    console.error('Error configuring physician schedule:', error);

    // Handle error
    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(error.message as string);
    }

    throw new Error('Failed to configure physician schedule');
  }
}

/**
 * Configure default schedule for a physician (Monday to Friday, 9 AM to 5 PM)
 * @param physicianId The physician ID
 * @returns Array of created schedule configurations
 */
export async function configureDefaultPhysicianSchedule(
  physicianId: string
): Promise<PhysicianScheduleResponse[]> {
  const defaultSchedules: PhysicianScheduleData[] = [
    // Monday to Friday
    ...Array.from({ length: 5 }, (_, i) => ({
      physician_id: physicianId,
      day_of_week: i + 1, // 1 = Monday, 5 = Friday
      start_time: '09:00',
      end_time: '17:00',
      slot_duration: 30,
      is_available: true
    }))
  ];

  const results: PhysicianScheduleResponse[] = [];

  for (const schedule of defaultSchedules) {
    try {
      const result = await configurePhysicianSchedule(schedule);
      results.push(result);
    } catch (error) {
      console.error(`Failed to configure schedule for day ${schedule.day_of_week}:`, error);
      // Continue with other days even if one fails
    }
  }

  return results;
}
