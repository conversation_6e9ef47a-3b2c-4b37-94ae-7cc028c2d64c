'use server';

import { get } from '../../../lib/api';
import { DashboardPatient } from '../types/dashboardTypes';

// Define the expected response structure from the API
export interface GetPatientsResponse {
  patients: DashboardPatient[];
  total: number;
}

/**
 * Fetches a list of patients with pagination.
 * @param page The page number to fetch (default: 1).
 * @param pageSize The number of patients per page (default: 10 for pagination).
 * @returns A promise resolving to the list of patients and total count.
 */
// Set to true to use mock data, false to use real API
const USE_MOCK_DATA = false;

export async function getPatients(
  page: number = 1,
  pageSize: number = 10
): Promise<GetPatientsResponse> {
  try {
    let response: any;

    if (USE_MOCK_DATA) {
      // Mock data for development/testing
      console.log('Using mock data for patients');
      const mockPatients = [
        {
          id: '637c739c-08d2-4cab-8947-19adb8b4ac70',
          user: {
            name: '<PERSON><PERSON><PERSON>',
            last_name: '<PERSON><PERSON><PERSON>',
            email: 'p_ma<PERSON><EMAIL>',
            phone: '**********',
            phone_prefix: '54',
            identification_number: '987654311',
            identification_type: 'DNI',
            age: 35,
            main_diagnostic_cie:
              'Cólera debido a Vibrio cholerae 01, biotipo cholerae',
            image: 'https://example.com/profile.jpg'
          },
          patient: {
            health_care_number: 'HC123456'
          }
        },
        {
          id: '737c739c-08d2-4cab-8947-19adb8b4ac71',
          user: {
            name: 'María',
            last_name: 'González',
            email: '<EMAIL>',
            phone: '**********',
            phone_prefix: '54',
            identification_number: '12345678',
            identification_type: 'DNI',
            age: 28,
            main_diagnostic_cie: 'Hipertensión esencial',
            image: 'https://example.com/profile2.jpg'
          },
          patient: {
            health_care_number: 'HC654321'
          }
        },
        {
          id: '837c739c-08d2-4cab-8947-19adb8b4ac72',
          user: {
            name: 'Carlos',
            last_name: 'Rodríguez',
            email: '<EMAIL>',
            phone: '**********',
            phone_prefix: '54',
            identification_number: '45678912',
            identification_type: 'DNI',
            age: 42,
            main_diagnostic_cie: 'Diabetes mellitus tipo 2',
            image: 'https://example.com/profile3.jpg'
          },
          patient: {
            health_care_number: 'HC789012'
          }
        },
        {
          id: '937c739c-08d2-4cab-8947-19adb8b4ac73',
          user: {
            name: 'Ana',
            last_name: 'López',
            email: '<EMAIL>',
            phone: '**********',
            phone_prefix: '54',
            identification_number: '23456789',
            identification_type: 'DNI',
            age: 31,
            main_diagnostic_cie: 'Asma bronquial',
            image: 'https://example.com/profile4.jpg'
          },
          patient: {
            health_care_number: 'HC345678'
          }
        },
        {
          id: 'a37c739c-08d2-4cab-8947-19adb8b4ac74',
          user: {
            name: 'Luis',
            last_name: 'Martínez',
            email: '<EMAIL>',
            phone: '**********',
            phone_prefix: '54',
            identification_number: '34567890',
            identification_type: 'DNI',
            age: 55,
            main_diagnostic_cie: 'Hipertensión arterial',
            image: 'https://example.com/profile5.jpg'
          },
          patient: {
            health_care_number: 'HC456789'
          }
        },
        {
          id: 'b37c739c-08d2-4cab-8947-19adb8b4ac75',
          user: {
            name: 'Elena',
            last_name: 'Fernández',
            email: '<EMAIL>',
            phone: '**********',
            phone_prefix: '54',
            identification_number: '45678901',
            identification_type: 'DNI',
            age: 29,
            main_diagnostic_cie: 'Migraña',
            image: 'https://example.com/profile6.jpg'
          },
          patient: {
            health_care_number: 'HC567890'
          }
        },
        {
          id: 'c37c739c-08d2-4cab-8947-19adb8b4ac76',
          user: {
            name: 'Roberto',
            last_name: 'Silva',
            email: '<EMAIL>',
            phone: '**********',
            phone_prefix: '54',
            identification_number: '56789012',
            identification_type: 'DNI',
            age: 48,
            main_diagnostic_cie: 'Artritis reumatoide',
            image: 'https://example.com/profile7.jpg'
          },
          patient: {
            health_care_number: 'HC678901'
          }
        },
        {
          id: 'd37c739c-08d2-4cab-8947-19adb8b4ac77',
          user: {
            name: 'Patricia',
            last_name: 'Morales',
            email: '<EMAIL>',
            phone: '**********',
            phone_prefix: '54',
            identification_number: '67890123',
            identification_type: 'DNI',
            age: 36,
            main_diagnostic_cie: 'Ansiedad generalizada',
            image: 'https://example.com/profile8.jpg'
          },
          patient: {
            health_care_number: 'HC789012'
          }
        },
        {
          id: 'e37c739c-08d2-4cab-8947-19adb8b4ac78',
          user: {
            name: 'Diego',
            last_name: 'Herrera',
            email: '<EMAIL>',
            phone: '**********',
            phone_prefix: '54',
            identification_number: '78901234',
            identification_type: 'DNI',
            age: 52,
            main_diagnostic_cie: 'Colesterol alto',
            image: 'https://example.com/profile9.jpg'
          },
          patient: {
            health_care_number: 'HC890123'
          }
        },
        {
          id: 'f37c739c-08d2-4cab-8947-19adb8b4ac79',
          user: {
            name: 'Valeria',
            last_name: 'Castro',
            email: '<EMAIL>',
            phone: '**********',
            phone_prefix: '54',
            identification_number: '89012345',
            identification_type: 'DNI',
            age: 27,
            main_diagnostic_cie: 'Gastritis crónica',
            image: 'https://example.com/profile10.jpg'
          },
          patient: {
            health_care_number: 'HC901234'
          }
        },
        {
          id: 'g37c739c-08d2-4cab-8947-19adb8b4ac80',
          user: {
            name: 'Fernando',
            last_name: 'Vega',
            email: '<EMAIL>',
            phone: '**********',
            phone_prefix: '54',
            identification_number: '90123456',
            identification_type: 'DNI',
            age: 44,
            main_diagnostic_cie: 'Lumbalgia crónica',
            image: 'https://example.com/profile11.jpg'
          },
          patient: {
            health_care_number: 'HC012345'
          }
        },
        {
          id: 'h37c739c-08d2-4cab-8947-19adb8b4ac81',
          user: {
            name: 'Sofía',
            last_name: 'Ramírez',
            email: '<EMAIL>',
            phone: '**********',
            phone_prefix: '54',
            identification_number: '01234567',
            identification_type: 'DNI',
            age: 33,
            main_diagnostic_cie: 'Fibromialgia',
            image: 'https://example.com/profile12.jpg'
          },
          patient: {
            health_care_number: 'HC123456'
          }
        }
      ];
      // Simulate pagination with mock data
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedPatients = mockPatients.slice(startIndex, endIndex);

      response = {
        items: paginatedPatients,
        meta: {
          totalItems: mockPatients.length,
          itemCount: paginatedPatients.length,
          itemsPerPage: pageSize,
          totalPages: Math.ceil(mockPatients.length / pageSize),
          currentPage: page
        }
      };
    } else {
      // Real API call - Based on documentation, the correct endpoint is /patient with pagination params
      const endpoint = `/patient?page=${page}&limit=${pageSize}`;
      console.log(`Fetching patients from: ${endpoint}`);

      response = await get<any>(endpoint);
    }

    console.log('Raw API response:', response);

    // Handle different response formats
    let patientsArray: any[] = [];
    let total = 0;

    if (response && Array.isArray(response.data)) {
      // New backend format with pagination metadata
      patientsArray = response.data;
      total = response.totalItems || 0;
      console.log(
        `Backend pagination format: ${patientsArray.length} patients, total: ${total}, page: ${response.currentPage}/${response.totalPages}`
      );
    } else if (Array.isArray(response)) {
      // Legacy format - direct array (fallback)
      patientsArray = response;
      total = response.length;
      console.log(
        'Legacy format: direct array with',
        patientsArray.length,
        'patients'
      );
    } else if (response && Array.isArray(response.patients)) {
      // Alternative format with patients property
      patientsArray = response.patients;
      total = response.total || response.patients.length;
      console.log(
        'Alternative format: patients property with',
        patientsArray.length,
        'patients'
      );
    } else if (response && Array.isArray(response.items)) {
      // Alternative format with items property
      patientsArray = response.items;
      total = response.meta?.totalItems || response.total || response.items.length;
      console.log(
        'Alternative format: items property with',
        patientsArray.length,
        'patients'
      );
    } else {
      console.warn('Received unexpected format for patients list:', response);
      return { patients: [], total: 0 };
    }

    // Transform the API response to match our DashboardPatient type
    const patients: DashboardPatient[] = patientsArray.map((p: any) => {
      return {
        id: String(p.id),
        name: p.name || 'Nombre no disponible',
        last_name: p.last_name || '',
        image: p.image,
        birthdate: p.birth_date || 'No disponible',
        age: p.age || 0,
        main_diagnostic_cie: p.main_diagnostic_cie || 'No especificado',
        email: p.email || 'No disponible',
        phone: p.phone || 'No disponible',
        prefix: p.prefix || '',
        identification_number: p.identification_number || '',
        identification_type: p.identification_type || '',
        health_care_number: p.health_care_number || ''
      };
    });

    console.log(`Successfully processed ${patients.length} patients.`);
    return {
      patients,
      total
    };
  } catch (error) {
    console.error('Error fetching patients:', error);
    if (error instanceof Error) {
      throw new Error(`Failed to fetch patients: ${error.message}`);
    }
    throw new Error('Failed to fetch patients due to an unexpected error.');
  }
}

