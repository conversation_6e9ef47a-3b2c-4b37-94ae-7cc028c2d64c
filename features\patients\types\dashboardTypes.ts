// Dashboard types for patient management

// Sort options for the patient list
export type SortDirection = 'asc' | 'desc';
export type SortCriteria = 'name' | 'age-asc' | 'age-desc' | 'recent' | null;

// Patient type with all required fields for the dashboard
export interface DashboardPatient {
  id: string;
  name: string;
  last_name: string;
  image?: string;
  age: number;
  birthdate: string; // Added for profile page compatibility
  main_diagnostic_cie: string;
  email: string;
  phone: string;
  prefix: string;
  identification_number: string;
  identification_type: string;
  health_care_number: string;
}

// Props for the patient table component
export interface PatientTableProps {
  patients: DashboardPatient[];
  onViewDetails: (patientId: string) => void;
  onViewClinicalHistory: (patientId: string) => void;
  onEditPatient: (patientId: string) => void;
  onDeletePatient: (patient: DashboardPatient) => void;
}

// Props for the patient actions dropdown
export interface PatientActionsProps {
  patient: DashboardPatient;
  onViewDetails: (patientId: string) => void;
  onViewClinicalHistory: (patientId: string) => void;
  onEditPatient: (patientId: string) => void;
  onDeletePatient: (patient: DashboardPatient) => void;
}

// Props for the search and filter component
export interface SearchAndFilterProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  sortBy: SortCriteria | null;
  onSortChange: (criteria: SortCriteria | null) => void;
  onAddPatient: () => void;
}
