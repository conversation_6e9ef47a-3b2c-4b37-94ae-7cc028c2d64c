# 🔧 Problemas del Backend que Necesitan Solución

## 🐛 **1. Solución para el Error de calculateAge en el Backend**

### Problema Identificado
El error ocurre en la función calculateAge cuando recibe un valor null:

```
Error en findAll: TypeError: Cannot read properties of null (reading 'getFullYear')
at calculateAge (C:\Users\<USER>\OneDrive\Escritorio\Trabajo\back\src\utils\fuctions.ts:3:45)
```

### Función Actual
```typescript
export function calculateAge(birthDate: Date): number {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }
  return age;
}
```

### Solución Recomendada: Modificar calculateAge
Cambiar la función en `back/src/utils/fuctions.ts`:

```typescript
export function calculateAge(birthDate: Date | null): number {
  // Manejar el caso cuando birthDate es null o undefined
  if (!birthDate) {
    return 0;
  }

  const today = new Date();
  const birth = new Date(birthDate);

  // Verificar que la fecha sea válida
  if (isNaN(birth.getTime())) {
    return 0;
  }

  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }

  return age;
}
```

### Por qué esta solución es mejor
- **Robustez**: Maneja casos donde birth_date puede ser null o undefined
- **Prevención**: Evita errores similares en otras partes del código que usen esta función
- **Validación**: Verifica que la fecha sea válida antes de procesarla
- **Mantenibilidad**: Centraliza el manejo de casos edge en una sola función

### Solución Temporal Aplicada
Actualmente se está usando una solución temporal en PatientService:

```typescript
age: user.birth_date ? calculateAge(user.birth_date) : 0,
```

Esta solución funciona pero requiere verificar birth_date en cada lugar donde se use calculateAge. La solución recomendada elimina esta necesidad.

---

## 📊 **2. Problema de Paginación en el Endpoint /patient**

### Problema Identificado
El endpoint `/patient` está devolviendo un array directo de pacientes en lugar de un objeto con metadata de paginación.

### Respuesta Actual del Backend
```json
[
  { "id": "1", "name": "Juan", ... },
  { "id": "2", "name": "María", ... },
  ...
]
```

### Respuesta Esperada para Paginación Correcta
```json
{
  "items": [
    { "id": "1", "name": "Juan", ... },
    { "id": "2", "name": "María", ... },
    ...
  ],
  "meta": {
    "totalItems": 50,
    "currentPage": 1,
    "totalPages": 5,
    "itemsPerPage": 10
  }
}
```

### Alternativas de Estructura Aceptables
También se puede usar cualquiera de estos formatos:

```json
{
  "patients": [...],
  "total": 50,
  "page": 1,
  "totalPages": 5
}
```

o

```json
{
  "data": [...],
  "pagination": {
    "total": 50,
    "page": 1,
    "limit": 10,
    "totalPages": 5
  }
}
```

### Impacto del Problema
- **Sin metadata de paginación**, el frontend no puede saber cuántos pacientes hay en total
- **La paginación desaparece** o muestra páginas incorrectas
- **Mala experiencia de usuario** al no poder navegar correctamente entre páginas

### Solución Temporal Aplicada
Se implementó una estimación en el frontend que funciona parcialmente, pero la solución definitiva debe ser en el backend.

---

## 🎯 **Prioridad de Implementación**

1. **ALTA**: Arreglar calculateAge (causa errores 500)
2. **MEDIA**: Implementar metadata de paginación (mejora UX significativamente)

Ambos problemas están afectando la funcionalidad del sistema en producción.
