'use client';
import React, { useState, useEffect } from 'react';

const NavConsultation = () => {
  const [activeSection, setActiveSection] = useState('condition-notes');

  const sections = [
    { id: 'condition-notes', label: 'Notas de Padecimiento' },
    { id: 'physical-examination', label: 'Exploración física' },
    { id: 'exam-physical', label: 'Examen físico' },
    { id: 'diagnosis-form', label: 'Diagnóstico y tratamiento' }
  ];

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    const scrollContainer = document.querySelector('.consultation-scroll-container');

    if (element && scrollContainer) {
      const containerRect = scrollContainer.getBoundingClientRect();
      const elementRect = element.getBoundingClientRect();
      const scrollTop = scrollContainer.scrollTop;

      const targetScrollTop = scrollTop + elementRect.top - containerRect.top - 20; // 20px offset

      scrollContainer.scrollTo({
        top: targetScrollTop,
        behavior: 'smooth'
      });
      setActiveSection(sectionId);
    }
  };

  useEffect(() => {
    const scrollContainer = document.querySelector('.consultation-scroll-container');

    const handleScroll = () => {
      if (!scrollContainer) return;

      const scrollTop = scrollContainer.scrollTop;
      const containerRect = scrollContainer.getBoundingClientRect();

      for (const section of sections) {
        const element = document.getElementById(section.id);
        if (element) {
          const elementRect = element.getBoundingClientRect();
          const relativeTop = elementRect.top - containerRect.top + scrollTop;
          const relativeBottom = relativeTop + element.offsetHeight;

          if (scrollTop + 100 >= relativeTop && scrollTop + 100 < relativeBottom) {
            setActiveSection(section.id);
            break;
          }
        }
      }
    };

    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      return () => scrollContainer.removeEventListener('scroll', handleScroll);
    }
  }, []);

  return (
    <div className="w-full border border-gray-200 rounded-lg bg-white">
      <nav className="p-2">
        <ul className="flex flex-row flex-wrap gap-1">
          {sections.map((section) => (
            <li key={section.id}>
              <button
                onClick={() => scrollToSection(section.id)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                  activeSection === section.id
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
                }`}
              >
                {section.label}
              </button>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default NavConsultation;
