'use client';

import {
  Edit,
  Trash2,
  Calendar,
  User,
  FileText,
  Eye
} from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { ConsultationActionsProps } from '../types/consultationTypes';

export function ConsultationActions({
  consultation,
  onViewConsultation,
  onViewPatientProfile,
  onEditConsultation,
  onDeleteConsultation
}: ConsultationActionsProps) {
  const router = useRouter();

  const handleViewConsultation = () => {
    onViewConsultation(consultation.id);
  };

  const handleViewPatientProfile = () => {
    onViewPatientProfile(consultation.patientId);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="text-blue-600 flex items-center justify-center w-8 h-8 hover:bg-gray-100 rounded-full transition-colors">
          ...
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        side="bottom"
        align="start"
        className="w-48 bg-[#FBFBFB]"
      >
        <DropdownMenuItem
          onSelect={handleViewConsultation}
          className="hover:bg-blue-50 hover:text-blue-600 focus:bg-blue-50 focus:text-blue-600"
        >
          <Eye className="mr-2 h-4 w-4" />
          Ver Consulta
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={handleViewPatientProfile}
          className="hover:bg-blue-50 hover:text-blue-600 focus:bg-blue-50 focus:text-blue-600"
        >
          <User className="mr-2 h-4 w-4" />
          Ver Perfil
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onSelect={() => onEditConsultation(consultation.id)}
          className="text-blue-600 hover:bg-blue-50 focus:bg-blue-50"
        >
          <Edit className="mr-2 h-4 w-4" />
          Editar
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={() => onDeleteConsultation(consultation)}
          className="text-red-600 hover:bg-red-50 focus:bg-red-50"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Eliminar
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
