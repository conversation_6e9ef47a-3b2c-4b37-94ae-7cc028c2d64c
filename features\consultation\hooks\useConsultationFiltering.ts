import { useMemo } from 'react';
import { DashboardConsultation, ConsultationSortCriteria, ConsultationSortDirection } from '../types/consultationTypes';

interface UseConsultationFilteringProps {
  consultations: DashboardConsultation[];
  searchTerm: string;
  sortBy: ConsultationSortCriteria | null;
  sortDirection: ConsultationSortDirection;
}

export function useConsultationFiltering({
  consultations,
  searchTerm,
  sortBy,
  sortDirection
}: UseConsultationFilteringProps) {
  const filteredAndSortedConsultations = useMemo(() => {
    let filtered = [...consultations];

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter((consultation) =>
        consultation.patientName.toLowerCase().includes(searchLower) ||
        consultation.patientLastName.toLowerCase().includes(searchLower) ||
        consultation.reason.toLowerCase().includes(searchLower) ||
        consultation.doctorName.toLowerCase().includes(searchLower) ||
        `${consultation.patientName} ${consultation.patientLastName}`.toLowerCase().includes(searchLower)
      );
    }

    // Apply sorting
    if (sortBy) {
      filtered.sort((a, b) => {
        switch (sortBy) {
          case 'patient-name': {
            const nameA = `${a.patientName} ${a.patientLastName}`.toLowerCase();
            const nameB = `${b.patientName} ${b.patientLastName}`.toLowerCase();
            return sortDirection === 'asc' 
              ? nameA.localeCompare(nameB)
              : nameB.localeCompare(nameA);
          }
          case 'date-asc': {
            const dateA = new Date(a.consultationDate);
            const dateB = new Date(b.consultationDate);
            return dateA.getTime() - dateB.getTime();
          }
          case 'date-desc': {
            const dateA = new Date(a.consultationDate);
            const dateB = new Date(b.consultationDate);
            return dateB.getTime() - dateA.getTime();
          }
          case 'recent': {
            const dateA = new Date(a.consultationDate);
            const dateB = new Date(b.consultationDate);
            return dateB.getTime() - dateA.getTime();
          }
          default:
            return 0;
        }
      });
    }

    return filtered;
  }, [consultations, searchTerm, sortBy, sortDirection]);

  return {
    filteredAndSortedConsultations
  };
}
