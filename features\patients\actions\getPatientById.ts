'use server';

import { get } from '@/lib/api';
import { DashboardPatient } from '../types/dashboardTypes';

/**
 * Fetches a specific patient by ID
 * @param patientId The ID of the patient to fetch
 * @returns Promise resolving to the patient data
 */
export async function getPatientById(patientId: string): Promise<DashboardPatient | null> {
  try {
    // Try to get the patient from the backend API
    const response = await get<any>(`/patient/${patientId}`);
    
    if (response && response.user && response.patient) {
      // Transform the backend response to our DashboardPatient format
      const transformedPatient: DashboardPatient = {
        id: response.patient.id,
        name: response.user.name,
        last_name: response.user.lastname || '',
        email: response.user.email,
        phone: response.user.phone || '',
        birthdate: response.patient.birth_date || '',
        gender: response.patient.gender || '',
        main_diagnostic_cie: response.patient.main_diagnostic_cie || '',
        age: response.patient.age || null,
        // Add other fields as needed
        avatar: null,
        address: response.user.address || '',
        city: response.user.city || '',
        country: response.user.country || '',
        province: response.user.province || '',
        dni: response.patient.dni || '',
        health_care_number: response.patient.health_care_number || ''
      };
      
      return transformedPatient;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching patient by ID:', error);
    
    // Fallback: try to find in the list of patients
    try {
      const { getPatients } = await import('./getPatients');
      const result = await getPatients(1, 100); // Get first 100 patients
      const foundPatient = result.patients?.find(
        (p: DashboardPatient) => p.id === patientId
      );
      return foundPatient || null;
    } catch (fallbackError) {
      console.error('Fallback also failed:', fallbackError);
      return null;
    }
  }
}
