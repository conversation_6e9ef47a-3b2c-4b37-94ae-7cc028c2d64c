'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { createMedicalOrder } from '@/features/medical-orders/actions/medicalOrderActions';
import { MedicalOrderType, CreateMedicalOrderRequest } from '@/features/medical-orders/types/medicalOrderTypes';

const NewMedicalOrderPage = () => {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderType, setOrderType] = useState<MedicalOrderType>('medication');
  const [formData, setFormData] = useState<CreateMedicalOrderRequest>({
    patient_id: '',
    request_reason: '',
    description_type: '',
    application_date: new Date().toISOString().split('T')[0],
  });

  const orderTypeOptions = [
    { value: 'medication', label: 'Medicación' },
    { value: 'study-authorization', label: 'Autorización de Estudio' },
    { value: 'certification', label: 'Certificado' },
    { value: 'hospitalization-request', label: 'Solicitud de Hospitalización' },
    { value: 'appointment-request', label: 'Solicitud de Turno' },
    { value: 'medication-authorization', label: 'Autorización de Medicación' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.patient_id) {
      toast.error('Por favor seleccione un paciente');
      return;
    }

    setIsSubmitting(true);
    try {
      await createMedicalOrder(orderType, formData);
      toast.success('Orden médica creada exitosamente');
      router.push('/dashboard/ordenes-medicas');
    } catch (error) {
      console.error('Error creating medical order:', error);
      toast.error('Error al crear la orden médica');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof CreateMedicalOrderRequest, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex-shrink-0 bg-white border-b">
        <div className="p-6">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4 text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Regresar
          </Button>
          
          <h1 className="text-2xl font-bold text-gray-900">Nueva Orden Médica</h1>
        </div>
      </div>

      {/* Form Content - Scrollable */}
      <div className="flex-1 overflow-auto bg-gray-50">
        <div className="p-6">
          <div className="max-w-2xl mx-auto">
            <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border p-6 space-y-6">
              {/* Order Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo de Orden *
                </label>
                <select
                  value={orderType}
                  onChange={(e) => setOrderType(e.target.value as MedicalOrderType)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  {orderTypeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Patient ID */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ID del Paciente *
                </label>
                <input
                  type="text"
                  value={formData.patient_id}
                  onChange={(e) => handleInputChange('patient_id', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Ingrese el ID del paciente"
                  required
                />
              </div>

              {/* Request Reason */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Motivo de la Solicitud
                </label>
                <textarea
                  value={formData.request_reason || ''}
                  onChange={(e) => handleInputChange('request_reason', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                  placeholder="Describa el motivo de la solicitud"
                />
              </div>

              {/* Description Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descripción del Tipo
                </label>
                <input
                  type="text"
                  value={formData.description_type || ''}
                  onChange={(e) => handleInputChange('description_type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Descripción específica del tipo de orden"
                />
              </div>

              {/* Application Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Fecha de Aplicación
                </label>
                <input
                  type="date"
                  value={formData.application_date || ''}
                  onChange={(e) => handleInputChange('application_date', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Study Type ID - Only for study-authorization */}
              {orderType === 'study-authorization' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    ID del Tipo de Estudio
                  </label>
                  <input
                    type="number"
                    value={formData.cat_study_type_id || ''}
                    onChange={(e) => handleInputChange('cat_study_type_id', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="ID del tipo de estudio"
                  />
                </div>
              )}

              {/* Hospitalization Reason - Only for hospitalization-request */}
              {orderType === 'hospitalization-request' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Motivo de Hospitalización
                  </label>
                  <textarea
                    value={formData.hospitalization_reason || ''}
                    onChange={(e) => handleInputChange('hospitalization_reason', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={3}
                    placeholder="Describa el motivo de la hospitalización"
                  />
                </div>
              )}

              {/* Specialty ID - Only for appointment-request */}
              {orderType === 'appointment-request' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    ID de la Especialidad
                  </label>
                  <input
                    type="number"
                    value={formData.cat_speciality_id || ''}
                    onChange={(e) => handleInputChange('cat_speciality_id', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="ID de la especialidad"
                  />
                </div>
              )}

              {/* Submit Button */}
              <div className="flex justify-end space-x-4 pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={isSubmitting}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isSubmitting ? 'Creando...' : 'Crear Orden'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewMedicalOrderPage;
