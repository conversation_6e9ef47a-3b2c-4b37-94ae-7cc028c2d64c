import { 
  MedicalOrderType, 
  CreateMedicalOrderRequest, 
  MedicalOrderResponse, 
  CreateMedicalOrderResponse,
  MedicalOrder
} from '../types/medicalOrderTypes';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('authToken');
  return {
    'Content-Type': 'application/json',
    'Authorization': token ? `Bearer ${token}` : '',
  };
};

/**
 * Create a new medical order
 */
export async function createMedicalOrder(
  type: MedicalOrderType,
  data: CreateMedicalOrderRequest
): Promise<CreateMedicalOrderResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/medical-order?type=${type}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating medical order:', error);
    throw error;
  }
}

/**
 * Get all medical orders with pagination
 */
export async function getMedicalOrders(
  page: number = 1,
  limit: number = 10,
  type?: MedicalOrderType,
  patient_id?: string
): Promise<MedicalOrderResponse> {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (type) params.append('type', type);
    if (patient_id) params.append('patient_id', patient_id);

    const response = await fetch(`${API_BASE_URL}/medical-order?${params}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching medical orders:', error);
    throw error;
  }
}

/**
 * Get medical orders for a specific physician
 */
export async function getPhysicianMedicalOrders(
  page: number = 1,
  limit: number = 10,
  patient_id?: string,
  type?: MedicalOrderType
): Promise<MedicalOrderResponse> {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (patient_id) params.append('patient_id', patient_id);
    if (type) params.append('type', type);

    const response = await fetch(`${API_BASE_URL}/medical-order/physician?${params}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching physician medical orders:', error);
    throw error;
  }
}

/**
 * Get medical orders for a specific patient
 */
export async function getPatientMedicalOrders(
  page: number = 1,
  limit: number = 10,
  physician_id?: string,
  type?: MedicalOrderType,
  tenant_id?: string
): Promise<MedicalOrderResponse> {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (physician_id) params.append('physician_id', physician_id);
    if (type) params.append('type', type);
    if (tenant_id) params.append('tenant_id', tenant_id);

    const response = await fetch(`${API_BASE_URL}/medical-order/patient?${params}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching patient medical orders:', error);
    throw error;
  }
}

/**
 * Get a specific medical order by ID
 */
export async function getMedicalOrderById(id: string): Promise<MedicalOrder> {
  try {
    const response = await fetch(`${API_BASE_URL}/medical-order/${id}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching medical order:', error);
    throw error;
  }
}

/**
 * Update a medical order
 */
export async function updateMedicalOrder(
  id: string,
  data: { file?: string }
): Promise<{ message: string }> {
  try {
    const response = await fetch(`${API_BASE_URL}/medical-order/${id}`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating medical order:', error);
    throw error;
  }
}

/**
 * Delete a medical order
 */
export async function deleteMedicalOrder(id: string): Promise<{ message: string }> {
  try {
    const response = await fetch(`${API_BASE_URL}/medical-order/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error deleting medical order:', error);
    throw error;
  }
}
