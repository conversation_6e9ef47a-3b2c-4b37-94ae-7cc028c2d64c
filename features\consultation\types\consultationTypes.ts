// Dashboard types for consultation management

// Sort options for the consultation list
export type ConsultationSortDirection = 'asc' | 'desc';
export type ConsultationSortCriteria = 'patient-name' | 'date-asc' | 'date-desc' | 'recent' | null;

// Consultation type with all required fields for the dashboard
export interface DashboardConsultation {
  id: string;
  patientId: string;
  patientName: string;
  patientLastName: string;
  consultationDate: string;
  reason: string;
  type: 'presencial' | 'virtual';
  amount: string;
  status: 'completed' | 'scheduled' | 'cancelled';
  doctorName: string;
}

// Props for the consultation table component
export interface ConsultationTableProps {
  consultations: DashboardConsultation[];
  onViewConsultation: (consultationId: string) => void;
  onViewPatientProfile: (patientId: string) => void;
  onEditConsultation: (consultationId: string) => void;
  onDeleteConsultation: (consultation: DashboardConsultation) => void;
}

// Props for the consultation actions dropdown
export interface ConsultationActionsProps {
  consultation: DashboardConsultation;
  onViewConsultation: (consultationId: string) => void;
  onViewPatientProfile: (patientId: string) => void;
  onEditConsultation: (consultationId: string) => void;
  onDeleteConsultation: (consultation: DashboardConsultation) => void;
}

// Props for the search and filter component
export interface ConsultationSearchAndFilterProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  sortBy: ConsultationSortCriteria | null;
  onSortChange: (criteria: ConsultationSortCriteria | null) => void;
  onAddConsultation: () => void;
}

// Props for pagination component (reusing the same interface as patients)
export interface ConsultationsPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  totalItems: number;
  itemsPerPage: number;
  currentPageItemsCount: number;
}
