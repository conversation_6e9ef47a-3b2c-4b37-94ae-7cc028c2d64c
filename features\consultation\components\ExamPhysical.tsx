'use client';

import React, { useState } from 'react';
import { Trash } from 'lucide-react';
import useConsultationStore from '../context/consultationStore';

const ExamPhysical = () => {
  const {
    subsistemas,
    comments,
    addSubsistema,
    removeSubsistema,
    updateComment
  } = useConsultationStore();

  const OptionsSubsistems = [
    'Seleccionar subsistema',
    'Aparato Digestivo',
    'Aparato Reproductor',
    'Aparato Urinario',
    'Cardiaco y Vascular',
    'Dental',
    'Dermatológico',
    'Neurológico',
    'Osteoarticular',
    'Otorrinolaringológico',
    'Psiquiátrico y Psicológico',
    'Pulmonar o Respiratorio',
    'Sistema Linfático'
  ];
  const [inputSubsistema, setInputSubsistema] = useState('');

  const handleAddSubsistema = () => {
    if (
      inputSubsistema.trim() !== '' &&
      inputSubsistema !== 'Seleccionar subsistema' &&
      !subsistemas.includes(inputSubsistema)
    ) {
      addSubsistema(inputSubsistema);
      setInputSubsistema('');
    }
  };

  return (
    <div className="mx-auto w-full max-w-2xl rounded-lg border border-gray-300 bg-white p-4 min-h-[600px]">
      <h1 className="mb-2 text-sm font-semibold text-gray-800">
        Examen físico
      </h1>
      <div className="mb-4 flex flex-col gap-3 md:flex-row">
        <select
          name="subsistema"
          id="subsistema"
          value={inputSubsistema}
          onChange={(e) => setInputSubsistema(e.target.value)}
          className="w-full rounded border border-gray-300 bg-white p-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {OptionsSubsistems.map((option, index) => (
            <option key={index} value={option}>
              {option}
            </option>
          ))}
        </select>
        <button
          className="w-full rounded bg-blue-600 px-4 py-2 text-white transition hover:bg-blue-700 md:w-auto"
          onClick={handleAddSubsistema}
        >
          Agregar
        </button>
      </div>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        {subsistemas.map((subsistema, index) => (
          <div
            key={index}
            className="flex flex-col justify-between rounded border border-gray-300 bg-gray-100 p-3 shadow-sm"
          >
            <div className="mb-2 flex items-center justify-between">
              <p className="font-lg text-gray-800">{subsistema}</p>
              <button
                className="text-red-500 hover:text-red-700"
                onClick={() => removeSubsistema(subsistema)}
              >
                <Trash size={20} />
              </button>
            </div>

            <input
              type="text"
              placeholder="Agregar comentario"
              className="h-[60px] w-full rounded border border-gray-300 bg-white p-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={comments[subsistema] || ''}
              onChange={(e) => updateComment(subsistema, e.target.value)}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ExamPhysical;
